import { Link, Outlet } from "react-router-dom";
import { useState } from "react";
import Sidebar from "./components/shared/Sidebar";

const Home = () => {
  const [isOpen, setIsOpen] = useState(false);
  const toggle = () => {
    setIsOpen(!isOpen);
  };
  const close = () => {
    setIsOpen(false);
  };
  return (
    <div>
      <header className=" p-4 bg-white md:p-5">
        <div className=" items-center w-full px-10 mx-auto flex justify-between gap-3">
          <div className="w-fit ">
            <h1 className="scroll-m-20 text-center text-2xl font-extrabold tracking-tight text-balance">
              FieldEase
            </h1>
          </div>
          <nav className=" md:flex  w-fit hidden">
            <ul className="flex flex-row items-center gap-3">
              <li className="leading-7">
                <Link to="/">Home</Link>
              </li>
              <li className="leading-7">
                <Link to="/about">About</Link>
              </li>
              <li className="leading-7">
                <Link to="/contact">Contact</Link>
              </li>
            </ul>
          </nav>
          <button className="md:hidden" onClick={toggle}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
              />
            </svg>
          </button>
          {isOpen && (
            <nav className="md:hidden flex flex-col gap-3 w-full">
              <ul className="flex flex-col items-center gap-3">
                <li className="leading-7">
                  <Link to="/" onClick={close}>
                    Home
                  </Link>
                </li>
                <li className="leading-7">
                  <Link to="/about" onClick={close}>
                    About
                  </Link>
                </li>
                <li className="leading-7">
                  <Link to="/contact" onClick={close}>
                    Contact
                  </Link>
                </li>
              </ul>
            </nav>
          )}
        </div>
      </header>

      <Sidebar isOpen={isOpen} onClose={close} />

      <Outlet />
    </div>
  );
};

export default Home;
