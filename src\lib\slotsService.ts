// slots service to fetch slots from the server or a mock data source
export interface Slots {
  id: number;
  date: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
  isSelected: boolean;
}

const sampleSlots: Slots[] = [
  {
    id: 1,
    date: "July 29, 2025",
    startTime: "7:00am",
    endTime: "7:30am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 2,
    date: "July 30, 2025",
    startTime: "7:30am",
    endTime: "8:00am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 3,
    date: "July 30, 2025",
    startTime: "8:00am",
    endTime: "8:30am",
    price: 15000,
    isAvailable: false,
    isSelected: false,
  },
  {
    id: 4,
    date: "July 30, 2025",
    startTime: "8:30am",
    endTime: "9:00am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 5,
    date: "July 29, 2025",
    startTime: "9:00am",
    endTime: "9:30am",
    price: 15000,
    isAvailable: false,
    isSelected: false,
  },
  {
    id: 6,
    date: "July 30, 2025",
    startTime: "9:30am",
    endTime: "10:00am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 7,
    date: "July 30, 2025",
    startTime: "10:00am",
    endTime: "10:30am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 8,
    date: "July 30, 2025",
    startTime: "10:30am",
    endTime: "11:00am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
  {
    id: 9,
    date: "July 30, 2025",
    startTime: "11:00am",
    endTime: "11:30am",
    price: 15000,
    isAvailable: true,
    isSelected: false,
  },
];

export const slotsService = {
  getSlots(date: string) {
    // get all slots for a date
    return sampleSlots.filter((slot) => slot.date === date);
  },

  getSlot(slotId: number): Promise<Slots | undefined> {
    // get a single slot by id
    return Promise.resolve(sampleSlots.find((slot) => slot.id === slotId));
  },
};
